'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';

interface UploadedImage {
  url: string;
  publicId: string;
}

export default function CloudinaryTestPage() {
  const [lastUploadedImage, setLastUploadedImage] = useState<UploadedImage | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    await uploadFile(file);
  };

  const uploadFile = async (file: File) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Starting upload for file:', file.name);

      // Create FormData for the API request
      const formData = new FormData();
      formData.append('file', file);
      formData.append('uploadType', 'restaurant'); // Use restaurant type for testing
      formData.append('restaurantId', 'test-restaurant-' + Date.now()); // Mock restaurant ID

      // Upload via your server-side API
      const response = await fetch('/api/upload/cloudinary', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      console.log('Upload successful:', result);

      setLastUploadedImage({
        url: result.secure_url,
        publicId: result.public_id,
      });

    } catch (error) {
      console.error('Upload error:', error);
      setError(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🌤️ Cloudinary Image Upload Test
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Test image uploads using server-side API with automatic optimization and transformations.
          </p>
        </div>

        {/* Upload Section */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
            📤 Cloudinary Image Upload Test
          </h2>

          <div className="flex flex-col items-center">
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
            />

            <button
              onClick={handleUploadClick}
              disabled={isLoading}
              className={`
                px-8 py-4 rounded-lg font-semibold text-white transition-all duration-200
                ${isLoading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-[#f3a823] hover:bg-[#ef7b06] shadow-lg hover:shadow-xl'
                }
              `}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Uploading...
                </div>
              ) : (
                '📁 Select Image to Upload'
              )}
            </button>

            <p className="text-sm text-gray-500 mt-4 text-center">
              Supports: JPG, PNG, WebP, GIF (max 10MB)<br />
              Uses server-side API for secure upload
            </p>

            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">
                  <strong>Error:</strong> {error}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Last Uploaded Image Section */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
            🖼️ That Last Uploaded Test Image
          </h2>

          {lastUploadedImage ? (
            <div className="space-y-6">
              {/* Image Display */}
              <div className="flex justify-center">
                <div className="relative w-full max-w-md aspect-square rounded-lg overflow-hidden shadow-lg">
                  <Image
                    src={lastUploadedImage.url}
                    alt="Last uploaded image"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                </div>
              </div>

              {/* Image Details */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Upload Details:</h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Public ID:</span>
                    <span className="ml-2 text-gray-600 font-mono break-all">{lastUploadedImage.publicId}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Cloudinary URL:</span>
                    <span className="ml-2 text-blue-600 font-mono break-all">{lastUploadedImage.url}</span>
                  </div>
                  <div className="pt-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      ✅ Successfully uploaded to Cloudinary
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                <span className="text-4xl text-gray-400">📷</span>
              </div>
              <p className="text-gray-500 text-lg">No image uploaded yet</p>
              <p className="text-gray-400 text-sm mt-2">
                Upload an image above to see it displayed here
              </p>
            </div>
          )}
        </div>
      </div>
    </div>

  );
}
